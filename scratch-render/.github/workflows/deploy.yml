name: Deploy playground

on:
  workflow_dispatch:
  push:
    branches: [develop]

concurrency:
  group: "deploy"
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
      - name: Install dependencies
        run: npm ci
      - name: Build playground
        run: npm run build
      - name: Build docs
        run: npm run docs
        # We expect to see syntax errors from the old jsdoc cli not understanding some of our syntax
        # It will still generate what it can, so it's safe to ignore the error
        continue-on-error: true
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./playground/

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    permissions:
      pages: write
      id-token: write
    runs-on: ubuntu-latest
    needs: build
    steps:
    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4
