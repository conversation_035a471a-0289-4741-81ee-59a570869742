/* generated by pull.js */
const manifest = {
  "editorOnly": true,
  "noTranslations": true,
  "name": "Do not shift pasted items",
  "description": "<PERSON><PERSON> copied items at their original position instead of shifted slightly in the costume editor.",
  "info": [
    {
      "text": "This behavior can also be achieved without this addon by Alt+Clicking the item.",
      "id": "vanilla"
    }
  ],
  "credits": [
    {
      "name": "GarboMuffin"
    }
  ],
  "dynamicDisable": true,
  "userscripts": [
    {
      "url": "userscript.js"
    }
  ],
  "tags": [],
  "enabledByDefault": false
};
export default manifest;
