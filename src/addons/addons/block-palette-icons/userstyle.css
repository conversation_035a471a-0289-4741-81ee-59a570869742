.scratchCategoryItemBubble {
  position: relative;
}

.scratchCategoryItemBubble::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.scratchCategoryId-motion .scratchCategoryItemBubble::after {
  background-image: url(icons/motion_icon.svg);
}

.scratchCategoryId-looks .scratchCategoryItemBubble::after {
  background-image: url(icons/looks_icon.svg);
}

.scratchCategoryId-sound .scratchCategoryItemBubble::after {
  background-image: url(icons/sound_icon.svg);
}

.scratchCategoryId-events .scratchCategoryItemBubble::after {
  background-image: url(icons/events_icon.svg);
}

.scratchCategoryId-control .scratchCategoryItemBubble::after {
  background-image: url(icons/control_icon.svg);
}

.scratchCategoryId-sensing .scratchCategoryItemBubble::after {
  background-image: url(icons/sensing_icon.svg);
}

.scratchCategoryId-operators .scratchCategoryItemBubble::after {
  background-image: url(icons/operators_icon.svg);
}

.scratchCategoryId-variables .scratchCategoryItemBubble::after {
  background-image: url(icons/variables_icon.svg);
}

.scratchCategoryId-lists .scratchCategoryItemBubble::after {
  background-image: url(icons/list_icon.svg);
}

.scratchCategoryId-myBlocks .scratchCategoryItemBubble::after {
  background-image: url(icons/block_icon.svg);
}
