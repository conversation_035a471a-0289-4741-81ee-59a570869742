/* generated by pull.js */
const manifest = {
  "editorOnly": true,
  "noTranslations": true,
  "name": "Two-column category menu",
  "description": "Splits the block category menu into two columns and moves it to the top of the block palette, like in Scratch 2.0.",
  "credits": [
    {
      "name": "TheColaber",
      "link": "https://scratch.mit.edu/TheColaber"
    }
  ],
  "tags": [
    "theme"
  ],
  "dynamicDisable": true,
  "userscripts": [
    {
      "url": "userscript.js"
    }
  ],
  "userstyles": [
    {
      "url": "style.css"
    }
  ]
};
export default manifest;
