.clone-container-container {
  display: none;
  align-items: center;
  padding: 0.25rem;
  user-select: none;
  color: #a065ff;
}

.clone-container {
  font-size: 0.625rem;
  font-weight: bold;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  white-space: nowrap;
}

.clone-icon {
  margin: 0.25rem;
  display: inline-block;
  background-image: url("./cat.svg");
  height: 16px;
  width: 16px;
}

.clone-container-container[data-count="none"] {
  display: none;
}

.clone-container-container[data-count="full"] {
  color: #ff6680;
}

.clone-container-container[data-count="full"] .clone-icon {
  background-image: url("./300cats.svg");
}

.clone-count::after {
  content: attr(data-str);
}

.sa-small-stage .clone-container-container {
  display: none !important;
}
