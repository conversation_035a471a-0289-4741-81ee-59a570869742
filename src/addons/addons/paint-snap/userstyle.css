.sa-paint-snap-button {
  position: relative;
}
.sa-paint-snap-button:focus-within {
  background-color: var(--editorDarkMode-primary-transparent35, hsla(260, 60%, 60%, 0.35));
}
.sa-paint-snap-button[data-enabled="true"] .sa-paint-snap-image {
  filter: brightness(0) invert(1);
}
.sa-paint-snap-button[data-enabled="true"] {
  background-color: var(--looks-secondary);
}

.sa-paint-snap-group {
  position: relative;
  flex-direction: row;
}

.sa-paint-snap-settings-wrapper {
  position: absolute;
  justify-items: center;
  left: 50%;
  width: 1.95rem;
  height: 1.95rem;
  display: grid;
}

.sa-paint-snap-settings {
  position: absolute;
  bottom: 100%;
  /* based on the styles for the color dropdown */
  padding: 4px;
  border-radius: 4px;
  border: 1px solid var(--ui-black-transparent);
  box-shadow: 0px 0px 8px 1px rgba(0, 0, 0, 0.3);
  transition-property: bottom, opacity;
  transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
  opacity: 0;
  pointer-events: none;
  background: var(--ui-modal-background);
  color: var(--ui-text-primary);
  min-height: 100%;
  min-width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.25em;
}
.sa-paint-snap-settings[data-visible="true"] {
  bottom: calc(100% + 22px);
  pointer-events: auto;
  opacity: 1;
  z-index: 200;
}

.sa-paint-snap-settings-line {
  display: flex;
  justify-content: flex-end;
  align-items: baseline;
}

.sa-paint-snap-settings-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  text-align: center;
  border: 0;
  background: transparent;
  -moz-appearance: textfield;
  border: 0;
  outline: 0;
}

.sa-paint-snap-settings-input::-webkit-outer-spin-button,
.sa-paint-snap-settings-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.sa-paint-snap-settings-tip {
  position: absolute;
  bottom: 0;
  transform: translateY(100%);
  right: calc(50% - 7px);
}
.sa-paint-snap-settings-polygon {
  fill: var(--ui-modal-background);
  stroke: var(--ui-black-transparent);
}

.sa-paint-snap-settings-separator {
  flex-grow: 1;
  border-bottom: 1px solid currentColor;
  margin: 4px;
  opacity: 0.25;
}

.sa-paint-snap-settings-section {
  display: flex;
  flex-direction: column;
  gap: 0.25em;
  align-items: flex-end;
  width: 100%;
}

.sa-paint-snap-settings-section-title {
  font-size: 1.2em;
  font-weight: 500;
  align-self: flex-start;
  margin-left: 4px;
}

.sa-paint-snap-settings-label {
  white-space: nowrap;
}

.sa-paint-snap-image[data-shrink="true"] {
  height: 1em;
  width: 1em;
}
