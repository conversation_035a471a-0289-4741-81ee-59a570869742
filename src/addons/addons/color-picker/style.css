.sa-color-picker {
  display: flex;
}

.sa-color-picker-code {
  margin: 8px 0;
}

.sa-color-picker-paint {
  margin-top: 16px;
  margin-bottom: 4px;
}

.sa-color-picker > .sa-color-picker-color {
  border: none;
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
  padding: 0;
  padding-left: 0.6rem;
  padding-right: 0.4rem;
  margin-left: 0.5rem;
  outline: none;
  box-sizing: border-box;
  width: 3rem;
  height: 2rem;
}

.sa-color-picker > .sa-color-picker-text {
  box-sizing: border-box;
  width: calc(150px - 3rem);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

[dir="rtl"] .sa-color-picker > .sa-color-picker-color {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .sa-color-picker > .sa-color-picker-text {
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

body.sa-hide-eye-dropper-background div[class*="stage_color-picker-background"] {
  /* Do not show eye dropper background if the color picker is "fake" */
  display: none;
}
