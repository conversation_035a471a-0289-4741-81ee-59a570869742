{"cat-blocks/@description": "Regresa los cat blocks del editor, lanzados inicialmente en April Fools 2020.", "cat-blocks/@info-watch": "La opción \"mirar cursor\" puede afectar el rendimiento cuando está abierto el editor.", "cat-blocks/@settings-name-watch": "Mirar cursor", "editor-devtools/@description": "Añade nuevas opciones de menú al editor: copiar/pegar bloques, mejor ordenado de bloques, ¡y más!", "editor-devtools/@name": "Herramientas de desarrollador", "editor-devtools/@settings-name-enableCleanUpPlus": "<PERSON><PERSON><PERSON> \"Ordenar Bloques\"", "editor-devtools/@settings-name-enablePasteBlocksAtMouse": "Pegar bloques en la posición del cursor", "find-bar/@description": "Agrega una barra de búsqueda al lado de la pestaña de sonidos para encontrar y saltar a scripts, disfraces y sonidos. Use Ctrl+Izquierda y Ctrl+Derecha en el área de código para navegar a la posición visitada anterior o siguiente luego de usar la barra de búsqueda.", "find-bar/@info-developer-tools": "Este addon era parte de \"herramientas de desarrollador\", pero se ha mudado aquí.", "find-bar/@name": "Barra de búsqueda en editor", "middle-click-popup/@description": "Click medio o Shift+click (Mayús.+click) en el área de código o presione Ctrl+Espacio para hacer aparecer una ventana de búsqueda. Escriba nombres de bloques (o partes de ellos) y arrástrelos al área de código para añadirlos a su proyecto. Para prevenir que se cierre este menú, mantenga Shift (Mayús.) mientras arrastre los bloques fuera de él.", "middle-click-popup/@info-developer-tools": "Este addon era parte de \"herramientas de desarrollador\", pero se ha mudado aquí.", "middle-click-popup/@name": "Insertar bloques por nombre", "middle-click-popup/@settings-name-popup_max_height": "Altura Máxima del Popup", "middle-click-popup/@settings-name-popup_scale": "Tamaño de los Bloques del Popup", "middle-click-popup/@settings-name-popup_width": "<PERSON><PERSON>", "jump-to-def/@description": "Le permite saltar a la definición de un bloque personalizado usando el botón del medio del ratón o presionando Shift+Click en el bloque.", "jump-to-def/@info-developer-tools": "Este addon era parte de \"herramientas de desarrollador\", pero se ha mudado aquí.", "jump-to-def/@name": "Saltar a definición de bloque personalizado", "reorder-custom-inputs/@description": "Permite reordenar los parámetros de bloques personalizados en la pantalla \"Crear un bloque\".", "reorder-custom-inputs/@name": "Entradas de bloques personalizados reorganizables", "editor-searchable-dropdowns/@description": "Le permite buscar entre las opciones en menús desplegables de bloques.", "editor-searchable-dropdowns/@name": "Búsqueda de menú desplegable", "data-category-tweaks-v2/@description": "Proporciona retoques para la categoría de bloques \"datos\" (variables).", "data-category-tweaks-v2/@name": "Retoques en categoría \"datos\"", "data-category-tweaks-v2/@settings-name-moveReportersDown": "Mover bloques de datos arriba de la lista de variables", "data-category-tweaks-v2/@settings-name-separateListCategory": "Categoría Listas Separada", "data-category-tweaks-v2/@settings-name-separateLocalVariables": "Separar Variables Locales", "block-palette-icons/@description": "Agrega íconos dentro de los círculos de colores que identifican las categorías de bloques.", "block-palette-icons/@name": "Íconos de categoría en paleta de bloques", "hide-flyout/@description": "Oculta la paleta de bloques a menos que no esté poniendo el cursor obre ella. Haga click en el ícono de bloqueo para mantenerlo en el lugar temporalmente. O use \"click de categoría\" para mostrarlo/ocultarlo cuando haga click en una categoría de bloques.", "hide-flyout/@info-hoverExplanation": "El modo \"cursor sobre área de paleta\" solo extiende el área de visión. Si quiere poder arrastrar bloques a este área sin que se tiren a la basura, use alguno de los otros modos.", "hide-flyout/@name": "Ocultar paleta de bloques automáticamente", "hide-flyout/@settings-name-lockLoad": "Abierto por defecto", "hide-flyout/@settings-name-speed": "Velocidad de animación", "hide-flyout/@settings-name-toggle": "Activar/desactivar en...", "hide-flyout/@settings-select-speed-default": "Por defecto", "hide-flyout/@settings-select-speed-long": "<PERSON><PERSON>", "hide-flyout/@settings-select-speed-none": "Instantánea", "hide-flyout/@settings-select-speed-short": "<PERSON><PERSON><PERSON><PERSON>", "hide-flyout/@settings-select-toggle-category": "Click en categoría", "hide-flyout/@settings-select-toggle-cathover": "Cursor sobre categoría", "hide-flyout/@settings-select-toggle-hover": "Cursor sobre área de paleta", "mediarecorder/@description": "Agrega un botón \"empezar grabación\" al menú del editor que le permite grabar el escenario del proyecto.", "mediarecorder/@name": "Grabador de video de proyectos", "drag-drop/@description": "Le permite arrastrar imágenes y sonidos de su administrador de archivos y soltarlos en el panel de objetos o la lista de disfraces/sonidos. También puede arrastrar archivos de texto a listas y cajas de respuesta.", "drag-drop/@name": "Arrastrar y soltar archivos", "drag-drop/@settings-name-use-hd-upload": "Usar subidas HD", "debugger/@description": "Agrega una ventana nueva \"depurador\" al editor. Le permite loguear mensajes a la pestaña \"Logs\" de la ventana del depurador usando los bloques \"loguear\", \"advertir\" y \"error\". El bloque \"breakpoint\" pausará el proyecto cuando se ejecute. Todos las pilas de bloques en ejecución pueden ser vistos en la pestaña \"Hilos\" de la ventana del depurador y al pausarse el botón \"Paso\" puede ser usado para ejecutar el siguiente bloque. Un gráfico de fotogramas por segundo y número de clones puede ser visto en la pestaña \"Rendimiento\".", "debugger/@name": "Depurador", "debugger/@settings-name-fancy_graphs": "Gráficos animados (puede afectar rendimiento)", "debugger/@settings-name-log_broadcasts": "<PERSON><PERSON><PERSON> mensajes enviados", "debugger/@settings-name-log_clear_greenflag": "Borrar logs al tocar bandera verde", "debugger/@settings-name-log_clone_create": "Loguear creación de clones", "debugger/@settings-name-log_failed_clone_creation": "<PERSON><PERSON>ar al exceder límite de clones", "debugger/@settings-name-log_greenflag": "<PERSON><PERSON><PERSON> clicks a bandera verde", "pause/@description": "Añade un botón al lado de la bandera verde para pausar el proyecto.", "pause/@info-keybind": "El proyecto también puede ser pausado con Alt+X (Option+X en macOS).", "pause/@name": "Botón de pausa", "mute-project/@description": "Presione Ctrl+Click sobre la bandera verde para silenciar o desilenciar el proyecto.", "mute-project/@info-macOS": "En macOS, use la tecla Cmd en vez de Ctrl.", "mute-project/@name": "Modo silenciado de reproductor de proyectos", "vol-slider/@description": "Agrega un control deslizante de volumen al lado de los controles de la bandera verde.", "vol-slider/@name": "Deslizador de volumen del proyecto", "vol-slider/@settings-name-always": "<PERSON><PERSON><PERSON><PERSON> des<PERSON>", "vol-slider/@settings-name-defVol": "Volumen por defecto", "clones/@description": "Agrega un contador de clones arriba del escenario en el editor que muestra la cantidad total de clones del proyecto.", "clones/@name": "Contador de clones", "clones/@settings-name-projectpage": "Mostrar en página de proyecto", "clones/@settings-name-showicononly": "Mostrar í<PERSON> solam<PERSON>", "mouse-pos/@description": "Muestra la posición x/y del ratón arriba del escenario en el editor.", "mouse-pos/@name": "Mostrar posición del ratón", "color-picker/@description": "Agrega una entrada de colores hex a los selectores de color.", "color-picker/@name": "Selector de color hex", "remove-sprite-confirm/@description": "Le pregunta si está seguro cuando borre un objecto en un proyecto", "remove-sprite-confirm/@info-restoretip": "Consejo: si elimina accidentalmente un objeto, disfraz o sonido, puede deshacerhaciendo click en Editar en la barra de menú y luego en Restaurar.", "remove-sprite-confirm/@name": "Confirmación de eliminación de objeto", "block-count/@description": "Muestra el número total de bloques de un proyecto en la barra de menú del editor. Previamente parte de \"contador de objetos y scripts\".", "block-count/@name": "Contador de bloques", "onion-skinning/@description": "Muestra capas transparentes de los disfraces anteriores o siguientes mientras edita un disfraz. Controlado con los botones debajo del editor de disfraces, al lado de los botones de zoom.", "onion-skinning/@name": "Capa sobre capa (onion skinning)", "onion-skinning/@settings-name-afterTint": "Teñir color de disfraz siguiente", "onion-skinning/@settings-name-beforeTint": "Teñir color de disfraz anterior", "onion-skinning/@settings-name-default": "Activar por defecto", "onion-skinning/@settings-name-layering": "Método de superposición por defecto", "onion-skinning/@settings-name-mode": "<PERSON><PERSON> por defecto", "onion-skinning/@settings-name-next": "Valor predeterminado de disfraces siguientes", "onion-skinning/@settings-name-opacity": "Opacidad (%)", "onion-skinning/@settings-name-opacityStep": "Salto de Opacidad (%)", "onion-skinning/@settings-name-previous": "Valor predeterminado de disfraces anteriores", "onion-skinning/@settings-select-layering-behind": "<PERSON><PERSON><PERSON>", "onion-skinning/@settings-select-layering-front": "<PERSON><PERSON>", "onion-skinning/@settings-select-mode-merge": "Combinar imagenes", "onion-skinning/@settings-select-mode-tint": "Teñir color", "paint-snap/@description": "Ajusta objetos en el editor de disfraces a cuadros de delimitación y nodos de vectores.", "paint-snap/@name": "Ajustar posición del editor de disfraces ", "paint-snap/@settings-name-boxCenter": "Ajustar desde centro de caja de selección", "paint-snap/@settings-name-boxCorners": "Ajustar desde esquinas de caja de selección", "paint-snap/@settings-name-boxEdgeMids": "Ajustar desde puntos medios de lados de caja de selección", "paint-snap/@settings-name-enable-default": "Habilitar por defecto", "paint-snap/@settings-name-guide-color": "Color de la guía del ajuste", "paint-snap/@settings-name-objectCenters": "Ajustar a centros de objetos", "paint-snap/@settings-name-objectCorners": "Ajustar a esquinas de objetos", "paint-snap/@settings-name-objectEdges": "Ajustar a lados de objetos", "paint-snap/@settings-name-objectMidlines": "Ajustar a líneas centrales de objetos", "paint-snap/@settings-name-pageAxes": "Ajustar a ejes x e y de la página", "paint-snap/@settings-name-pageCenter": "Ajustar a centro de página", "paint-snap/@settings-name-pageCorners": "Ajustar a esquinas de la página", "paint-snap/@settings-name-pageEdges": "Ajustar a lados de la página", "paint-snap/@settings-name-threshold": "Distancia de ajuste", "default-costume-editor-color/@description": "Cambia los colores predeterminados y el tamaño del borde utilizados por el editor de disfraces.", "default-costume-editor-color/@name": "Color predeterminado personalizable en el editor de disfraces", "default-costume-editor-color/@settings-name-fill": "Color de relleno predeterminado", "default-costume-editor-color/@settings-name-persistence": "Usar color anterior en vez de reiniciar al cambiar de herramienta", "default-costume-editor-color/@settings-name-stroke": "Color de borde predeterminado", "default-costume-editor-color/@settings-name-strokeSize": "Tam<PERSON><PERSON> de borde predeterminado", "bitmap-copy/@description": "Le permite copiar una imagen bitmap en el editor de disfraces al portapapeles de su sistema, para que la puedas pegar en otros sitios web o programas.", "bitmap-copy/@info-norightclick": "\"Click derecho → copiar\" no está soportado. Debe presionar Ctrl+C cuando la imagen bitmap este seleccionada.", "bitmap-copy/@name": "Copiar imágenes bitmap", "2d-color-picker/@description": "Remplaza los deslizadores de saturación y brillo con un selector de color 2D. Presione Shift mientras arrastra el cursor para cambiar los valores en un solo eje.", "2d-color-picker/@name": "Selector de color 2D", "paint-skew/@description": "Para doblar un objeto, mantenga presionado Ctrl mientras lo escala desde uno de sus lados. Mantenga Alt mientras dobla un objeto para doblarlo desde su centro.", "paint-skew/@info-useCmdOnMac": "En macOS, use la tecla Command en vez de la tecla Ctrl y la tecla Option en vez de la tecla Alt.", "paint-skew/@name": "Doblar en editor de disfraces", "better-img-uploads/@description": "Agrega un nuevo botón arriba del botón \"subir disfraz\" que automáticamente convierte imágenes bitmap subidas a imágenes SVG (vector) para evitar perder calidad.", "better-img-uploads/@info-notSuitableEdit": "Evite usar el botón de subida HD si tiene planeado editar la imagen luego de subirla.", "better-img-uploads/@name": "Subida de imágenes HD", "better-img-uploads/@settings-name-fitting": "Tamaño de imagen", "better-img-uploads/@settings-select-fitting-fill": "Estirar para llenar lienzo", "better-img-uploads/@settings-select-fitting-fit": "Achicar para encajar en lienzo", "better-img-uploads/@settings-select-fitting-full": "Tamaño original", "pick-colors-from-stage/@description": "Permite que el cuentagotas también pueda elegir colores del escenario.", "pick-colors-from-stage/@name": "Seleccionar colores del escenario con cuentagotas", "custom-block-shape/@description": "Ajuste el relleno, radio de esquinas y altura de la muesca de los bloques.", "custom-block-shape/@info-paddingWarning": "Si baja el valor de tamaño del relleno, cuando otros usuarios vean su proyecto, podría parecer que sus scripts se superponen.", "custom-block-shape/@name": "Forma de bloque personalizada", "custom-block-shape/@preset-description-default2": "Una apariencia similar a bloques de Scratch 2.0", "custom-block-shape/@preset-description-default3": "La apariencia regular de bloques de Scratch 3.0", "custom-block-shape/@preset-description-flat2": "Bloques de Scratch 2.0 sin muesca ni esquinas", "custom-block-shape/@preset-description-flat3": "Bloques de Scratch 3.0 sin muesca ni esquinas", "custom-block-shape/@preset-name-default2": "Bloques 2.0", "custom-block-shape/@preset-name-default3": "Bloques 3.0", "custom-block-shape/@preset-name-flat2": "2.0 Plano", "custom-block-shape/@preset-name-flat3": "3.0 Plano", "custom-block-shape/@settings-name-cornerSize": "Tamaño de esquinas (0-300%)", "custom-block-shape/@settings-name-notchSize": "Altura de muesca (0-150%)", "custom-block-shape/@settings-name-paddingSize": "<PERSON>a<PERSON> de relleno/padding (50-200%)", "editor-square-inputs/@settings-name-color": "Entradas de color", "editor-square-inputs/@settings-name-number": "Entradas numéricas", "editor-square-inputs/@settings-name-text": "Entradas de texto", "zebra-striping/@description": "Alterna entre tonos más claros y más oscuros a los bloques de la misma categoría anidados entre sí. Esto también es conocido como \"zebra striping\".", "zebra-striping/@name": "Alternar colores de bloques anidados", "zebra-striping/@settings-name-intensity": "Intensidad (0-100%)", "zebra-striping/@settings-name-shade": "Tonalidad", "zebra-striping/@settings-select-shade-darker": "Oscura", "zebra-striping/@settings-select-shade-lighter": "<PERSON>", "editor-compact/@description": "Disminuye el tamaño de los botones, campos de entrada, la barra de menú y otros elementos del editor, dejando más espacio para componentes como el área de código, el editor de disfraces y las vistas previas de disfraces, como ocurría en Scratch 2.0.", "editor-compact/@info-hide-icons-update": "Para esconder los íconos del menú, use la función \"Barra de menú personalizable\".", "editor-compact/@name": "Editor compacto", "editor-compact/@settings-name-hideLabels": "Esconder etiquetas de botones en editores de disfraces y sonidos", "custom-menu-bar/@description": "Permite esconder ciertos elementos de la barra de menú del editor, y quitar sus etiquetas o íconos.", "custom-menu-bar/@info-small-screens": "Las etiquetas de texto pueden ser reemplazadas automáticamente por íconos en ventanas pequeñas.", "custom-menu-bar/@settings-name-menu-labels": "Mostrar...", "custom-menu-bar/@settings-select-menu-labels-both": "Íconos y etiquetas", "custom-menu-bar/@settings-select-menu-labels-icons": "Solo <PERSON>", "custom-menu-bar/@settings-select-menu-labels-labels": "Solo etiquetas", "editor-theme3/@description": "Edite los colores para cada categoría de bloques del editor.", "editor-theme3/@name": "Colores de bloques personalizables", "editor-theme3/@preset-description-black": "Hace a los fondos de los bloques negros y a los bordes/textos de colores", "editor-theme3/@preset-description-contrast": "Paleta de colores de bloques de alto contraste de Scratch 3.0", "editor-theme3/@preset-description-dark": "Versiones aún más oscuras de los colores predeterminados", "editor-theme3/@preset-description-new-dark": "Versiones oscuras de los colores predeterminados que se ven bien en temas oscuros", "editor-theme3/@preset-description-original": "Los colores originales de Scratch 2.0", "editor-theme3/@preset-description-tweaks": "Los colores de Scratch 3.0 con Eventos, Control y Bloques personalizados similares a los colores de Scratch 2.0", "editor-theme3/@preset-name-black": "Negro", "editor-theme3/@preset-name-contrast": "<PERSON> contraste", "editor-theme3/@preset-name-dark": "<PERSON>ás oscuro", "editor-theme3/@preset-name-new-dark": "Oscuro", "editor-theme3/@preset-name-original": "Colores de Scratch 2.0", "editor-theme3/@preset-name-tweaks": "Scratch 3.0 retocado", "editor-theme3/@settings-name-Pen-color": "extensiones", "editor-theme3/@settings-name-comment-color": "Comentarios", "editor-theme3/@settings-name-custom-color": "personalizado", "editor-theme3/@settings-name-data-lists-color": "listas", "editor-theme3/@settings-name-events-color": "eventos", "editor-theme3/@settings-name-input-color": "Campos de bloques", "editor-theme3/@settings-name-looks-color": "apariencia", "editor-theme3/@settings-name-motion-color": "movimiento", "editor-theme3/@settings-name-operators-color": "operadores", "editor-theme3/@settings-name-sensing-color": "sensores", "editor-theme3/@settings-name-sounds-color": "sonidos", "editor-theme3/@settings-name-text": "Color del texto", "editor-theme3/@settings-select-text-black": "Negro", "editor-theme3/@settings-select-text-colorOnBlack": "Con color en fondo negro", "editor-theme3/@settings-select-text-colorOnWhite": "Con color en fondo blanco", "editor-theme3/@settings-select-text-white": "<PERSON>", "custom-block-text/@description": "Le permite personalizar el estilo del texto en los bloques, como ajustar el tamaño, usar texto en negrita o agregar una sombra.", "custom-block-text/@name": "Estilo de texto de bloques personalizado", "custom-block-text/@settings-name-bold": "Texto en negrita", "custom-block-text/@settings-name-shadow": "Sombra bajo texto", "custom-block-text/@settings-name-size": "<PERSON><PERSON><PERSON> del texto (%)", "editor-colored-context-menus/@description": "Hace que los menús al dar click derecho en un bloque sean coloridos.", "editor-colored-context-menus/@name": "Menús colorido<PERSON>", "editor-stage-left/@description": "Mueve el escenario al lado izquierdo del editor, como en Scratch 2.0.", "editor-stage-left/@info-reverseOrder": "Para cambiar la posición de los botones arriba del escenario, use el addon \"invertir orden de los controles del proyecto\".", "editor-stage-left/@name": "Mostrar escenario del lado izquierdo", "editor-buttons-reverse-order/@description": "Mueve la bandera verde y el botón de detener a la derecha, y el botón de pantalla completa a la izquierda, como en Scratch 2.0.", "editor-buttons-reverse-order/@name": "Invertir orden de los controles del proyecto", "variable-manager/@description": "Agrega una pestaña al lado de \"sonidos\" en el editor para que pueda fácilmente editar variables y listas.", "variable-manager/@name": "Gestor de variables", "search-sprites/@description": "Agrega una barra de búsqueda al panel de objetos para buscar objetos por nombre.", "search-sprites/@name": "Barra de búsqueda en panel de objetos", "sprite-properties/@description": "Esconde en panel de propiedades de sprite por defecto, como en Scratch 2.0. Use el botón de información en el sprite actualmente seleccionado o haga doble click en un sprite para mostrar el panel de propiedades otra vez. Para volver a esconderlo, use el botón de colapsar en el panel de propiedades o haga doble click en un sprite.", "sprite-properties/@name": "<PERSON><PERSON><PERSON> propiedades de sprites", "sprite-properties/@settings-name-autoCollapse": "Colapsar automáticamente cuando el cursor se aleja del panel de sprites", "sprite-properties/@settings-name-hideByDefault": "Colapsar panel por defecto", "sprite-properties/@settings-name-transitionDuration": "Velocidad de animación", "sprite-properties/@settings-select-transitionDuration-default": "Predeterminada", "sprite-properties/@settings-select-transitionDuration-long": "<PERSON><PERSON>", "sprite-properties/@settings-select-transitionDuration-none": "Instantánea", "sprite-properties/@settings-select-transitionDuration-short": "<PERSON><PERSON><PERSON><PERSON>", "gamepad/@description": "Interactúe con proyectos usando un control/mando de videojuegos mediante USB o Bluetooth.", "gamepad/@name": "Soporte de mando de videojuegos", "gamepad/@settings-name-hide": "Esconder botón de ajustes cuando no hay mandos conectados", "editor-sounds/@description": "Reproduce efectos de sonido cuando conecta o elimina bloques.", "editor-sounds/@name": "Efectos de sonido en editor", "folders/@description": "Agrega carpetas a listas de objetos, como también a las listas de disfraces y sonidos. Para crear una carpeta, haga click derecho en un objeto y seleccione \"crear carpeta\". Haga click en una carpeta para abrirla o cerrarla. Haga click derecho en un objeto para ver a qué carpetas lo puede mover, o alternativamente arrastre y suelte a una carpeta abierta. Esta función agrega \"[nombreDeLaCarpeta]//\" al principio del nombre de sus objetos.", "folders/@info-notice-folders-are-public": "Usuarios con esta función activada podrán ver las carpetas en su proyecto. Cualquier otra persona verá las listas de objetos normalmente (sin carpetas)", "folders/@name": "Carpetas de objetos", "block-switching/@description": "Haga click derecho en un bloque para cambiarlo por otro bloque relacionado.", "block-switching/@name": "Cambio entre bloques", "block-switching/@settings-name-control": "Bloques de control", "block-switching/@settings-name-customargs": "Parámetros de bloques personalizados", "block-switching/@settings-name-customargsmode": "Opciones mostradas de parámetros de bloques personalizados", "block-switching/@settings-name-data": "Bloques de variables", "block-switching/@settings-name-event": "Bloques de eventos", "block-switching/@settings-name-extension": "Bloques de extensiones", "block-switching/@settings-name-looks": "Bloques de apariencia", "block-switching/@settings-name-motion": "Bloques de movimiento", "block-switching/@settings-name-noop": "<PERSON><PERSON><PERSON> cambiar un bloque a sí mismo", "block-switching/@settings-name-operator": "Bloques de operadores", "block-switching/@settings-name-sensing": "Bloques de sensores", "block-switching/@settings-name-sound": "Bloques de sonido", "block-switching/@settings-select-customargsmode-all": "Parámetros en todos los bloques personalizados del objeto", "block-switching/@settings-select-customargsmode-defOnly": "Parámetros en bloque personalizado propio", "load-extensions/@description": "Muestra automáticamente música, lápiz y otras extensiones del editor en el menú de categorías del editor.", "load-extensions/@name": "Agregar extensiones automáticamente", "load-extensions/@settings-name-music": "Música", "load-extensions/@settings-name-pen": "Lá<PERSON>z", "load-extensions/@settings-name-text2speech": "Texto a Voz", "load-extensions/@settings-name-translate": "Traducir", "custom-zoom/@description": "Elija ajustes personalizados para el mínimo, máximo, velocidad y tamaño inicial del zoom en el área de código y esconda los controles automáticamente.", "custom-zoom/@name": "Zoom de área de código personalizado", "custom-zoom/@settings-name-autohide": "Esconder controles cuando el cursor no está sobre ellos", "custom-zoom/@settings-name-maxZoom": "Zoom Máximo (100-500%)", "custom-zoom/@settings-name-minZoom": "<PERSON><PERSON> (1-100%)", "custom-zoom/@settings-name-speed": "Velocidad de animación al esconder controles", "custom-zoom/@settings-name-startZoom": "Zoom inicial (50-500%)", "custom-zoom/@settings-name-zoomSpeed": "Velocidad de zoom (50-200%)", "custom-zoom/@settings-select-speed-default": "Predeterminada", "custom-zoom/@settings-select-speed-long": "<PERSON><PERSON>", "custom-zoom/@settings-select-speed-none": "Instantánea", "custom-zoom/@settings-select-speed-short": "<PERSON><PERSON><PERSON><PERSON>", "copy-reporter/@description": "Agrega la opción de copiar los valores mostrados por monitores de variables (desde el menú de click derecho) y globos de reporte de bloques.", "copy-reporter/@name": "Copiar valores del monitor", "initialise-sprite-position/@description": "Cambia el lugar donde los objetos recien creados son posicionados en el escenario.", "initialise-sprite-position/@name": "Posición predeterminada de nuevos objetos personalizable", "initialise-sprite-position/@settings-name-duplicate": "Posición de objetos duplicados", "initialise-sprite-position/@settings-name-library": "Posicionar objetos de la biblioteca en posición aleatoria", "initialise-sprite-position/@settings-name-x": "Posición X", "initialise-sprite-position/@settings-name-y": "Posición Y", "initialise-sprite-position/@settings-select-duplicate-custom": "Posición x/y específica", "initialise-sprite-position/@settings-select-duplicate-keep": "Igual al objeto original", "initialise-sprite-position/@settings-select-duplicate-randomize": "Aleatoria", "blocks2image/@description": "Haga click derecho en el área de código para exportar bloques como imágenes SVG/PNG.", "blocks2image/@name": "Guardar bloques como imagen", "remove-curved-stage-border/@description": "Quita los bordes curvos alrededor del escenario, permiti<PERSON><PERSON><PERSON> ver las esquinas.", "remove-curved-stage-border/@name": "Quitar curva del borde del escenario", "transparent-orphans/@description": "Ajuste la transparencia de los bloques del editor, con opciones separadas para bloques huérfanos (aquellos sin un bloque de evento en su parte superior) y bloques que están siendo arrastrados.", "transparent-orphans/@name": "Bloques transparentes", "transparent-orphans/@settings-name-block": "Transparencia de bloques (%)", "transparent-orphans/@settings-name-dragged": "Transparencia arrastrando (%)", "transparent-orphans/@settings-name-orphan": "Transparencia de huérfanos (%)", "paint-by-default/@description": "Cambia la acción por defecto de los botones \"Elegir un objeto/disfraz/fondo/sonido\", que abren la biblioteca por defecto.", "paint-by-default/@name": "Pintar disfraz por defecto", "paint-by-default/@settings-name-backdrop": "Agregar fondo", "paint-by-default/@settings-name-costume": "Agregar disfraz", "paint-by-default/@settings-name-sound": "Agregar sonido", "paint-by-default/@settings-name-sprite": "Agregar objeto", "paint-by-default/@settings-select-backdrop-library": "Biblioteca", "paint-by-default/@settings-select-backdrop-paint": "Pintar", "paint-by-default/@settings-select-backdrop-surprise": "Sorpresa", "paint-by-default/@settings-select-backdrop-upload": "Subir", "paint-by-default/@settings-select-costume-library": "Biblioteca", "paint-by-default/@settings-select-costume-paint": "Pintar", "paint-by-default/@settings-select-costume-surprise": "Sorpresa", "paint-by-default/@settings-select-costume-upload": "Subir", "paint-by-default/@settings-select-sound-library": "Biblioteca", "paint-by-default/@settings-select-sound-record": "<PERSON><PERSON><PERSON>", "paint-by-default/@settings-select-sound-surprise": "Sorpresa", "paint-by-default/@settings-select-sound-upload": "Subir", "paint-by-default/@settings-select-sprite-library": "Biblioteca", "paint-by-default/@settings-select-sprite-paint": "Pintar", "paint-by-default/@settings-select-sprite-surprise": "Sorpresa", "paint-by-default/@settings-select-sprite-upload": "Subir", "block-cherry-picking/@description": "Le permite arrastrar un único bloque del medio de un script (en vez de toda la pila adjunta debajo) mientras mantenga la tecla Ctrl.", "block-cherry-picking/@info-flipControls": "Si \"revertir controles\" está activado, agarrar bloques individualmente será el comportamiento predeterminado. Mantenga Ctrl para arrastrar la pila completa.", "block-cherry-picking/@info-macContextDisabled": "En macOS, use la tecla Cmd en vez de Ctrl.", "block-cherry-picking/@name": "Agarrar un único bloque con tecla Ctrl", "block-cherry-picking/@settings-name-invertDrag": "Revertir controles", "hide-new-variables/@description": "No mostrar monitores automáticamente para las nuevas variables o listas.", "hide-new-variables/@name": "Esconder variables nuevas", "editor-extra-keys/@description": "Añade más teclas a los menús desplegables de los bloques \"¿tecla () presionada?\" y \"al presionar tecla ()\", como enter, coma, y más. Estas teclas incluso funcionarán para usuarios que no tengan este addon.", "editor-extra-keys/@info-experimentalKeysWarn": "Las \"teclas experimentales\" incluyen signo igual, barra, punto y coma y más. Pueden no funcionar en todos los sistemas operativos o diseños de teclado.", "editor-extra-keys/@info-shiftKeysWarn": "Las \"teclas Shift\" incluyen teclas que típicamente requieren la tecla Shift y una tecla de número, como hashtag, símbolo de exclamación y más. Estas teclas solo funcionan con el bloque \"al presionar  tecla ()\" y no funcionan en todos los sistemas operativos o diseños de teclado.", "editor-extra-keys/@name": "Opciones de teclas extra", "editor-extra-keys/@settings-name-experimentalKeys": "Mostrar teclas experimentales", "editor-extra-keys/@settings-name-shiftKeys": "Mostrar teclas Shift", "hide-delete-button/@description": "Esconde el botón de eliminar (ícono de bote de basura) de objetos, disfraces y sonidos. Estos pueden seguirse eliminando utilizando el menú contextual al dar click derecho.", "hide-delete-button/@info-restoretip": "Consejo: si elimina accidentalmente un objeto, disfraz o sonido, puede deshacerhaciendo click en Editar en la barra de menú y luego en Restaurar.", "hide-delete-button/@name": "Esconder botón de eliminar", "hide-delete-button/@settings-name-costumes": "Disfraces y fondos", "hide-delete-button/@settings-name-sounds": "Sonidos", "hide-delete-button/@settings-name-sprites": "<PERSON><PERSON><PERSON><PERSON>", "no-script-bumping/@description": "Permite que los scripts sean movidos y modificados sin causar que scripts superpuestos se muevan.", "no-script-bumping/@name": "No espaciar automáticamente scripts superpuestos", "disable-stage-drag-select/@description": "Elimina la posibilidad de arrastrar objetos en el escenario, exceptuando aquellos fijados explícitamente como arrastrables. Presione Shift mientras arrastra un objeto para moverlo normalmente.", "disable-stage-drag-select/@name": "Objetos no arrastrables en el editor", "disable-stage-drag-select/@settings-name-drag_while_stopped": "Permit<PERSON> a<PERSON> si el proyecto está detenido", "move-to-top-bottom/@description": "Agrega opciones al menú de click derecho para mover un disfraz o sonido a la primera o última posición de la lista.", "move-to-top-bottom/@info-developer-tools": "Este addon era parte de \"herramientas de desarrollador\", pero se ha mudado aquí.", "move-to-top-bottom/@name": "Mover disfraz a la cima o al fondo", "move-to-top-layer/@description": "Shift+Click a un objeto en el panel de objetos para moverlo al frente (capa delantera) del escenario.", "move-to-top-layer/@name": "Mover objeto a capa delantera", "disable-paste-offset/@description": "Pega items copiados en su posición original en vez de movidos levemente en el editor de disfraces.", "disable-paste-offset/@info-vanilla": "Este comportamiento también puede ser logrado sin este addon mediante Alt+Click a un objeto.", "disable-paste-offset/@name": "No mover items pegados", "block-duplicate/@description": "Duplique rápidamente un script arrastrándolo mientras mantiene la tecla Alt. Apriete Ctrl también para solo duplicar un bloque en vez de toda la pila adjunta debajo.", "block-duplicate/@info-mac": "En macOS, use la tecla Option en vez de la tecla Alt y la tecla Command en vez de la tecla Control.", "block-duplicate/@name": "Duplicar script con tecla Alt", "rename-broadcasts/@description": "Añade una opción para renombrar mensajes en los menús desplegables de los bloques de transmisión de mensajes.", "rename-broadcasts/@name": "<PERSON><PERSON><PERSON>", "swap-local-global/@description": "Agrega más opciones al renombrar una variable o lista existente: permite cambiar entre \"Para todos los objetos\" y \"Solo para este objeto\" y si las variables son almacenadas en la nube. También agrega una nueva opción al hacer click derecho en una variable/lista para cambiar rápidamente su ámbito.", "swap-local-global/@name": "Cambiar variables entre \"Para todos los objetos\" y \"Solo para este objeto\"", "editor-comment-previews/@description": "Le permite previsualizar los contenidos de comentarios al poner el cursor sobre comentarios encogidos y bloques. Puede usar esto para ver comentarios que están fuera de la pantalla, identificar un bloque bucle desde el final por su previsualización, meter muchos comentarios en un lugar pequeño y más.", "editor-comment-previews/@name": "Previsualización de comentarios del editor", "editor-comment-previews/@settings-name-delay": "Tiempo de retardo", "editor-comment-previews/@settings-name-follow-mouse": "<PERSON><PERSON><PERSON> cursor", "editor-comment-previews/@settings-name-hover-view": "Poner el cursor sobre comentarios encogidos para previsualizar", "editor-comment-previews/@settings-name-hover-view-block": "Poner cursor sobre bloques para previsualizar comentarios enganchados", "editor-comment-previews/@settings-name-hover-view-procedure": "Poner cursor sobre bloques personalizados para previsualizar comentarios en su definición", "editor-comment-previews/@settings-name-reduce-animation": "Reducir animación", "editor-comment-previews/@settings-name-reduce-transparency": "Reducir transparencia", "editor-comment-previews/@settings-select-delay-long": "Largo", "editor-comment-previews/@settings-select-delay-none": "<PERSON><PERSON><PERSON>", "editor-comment-previews/@settings-select-delay-short": "Corto", "columns/@description": "Divide el menú de categorías de bloques en dos columnas y lo mueve a la cima de la paleta de bloques, como en Scratch 2.0.", "columns/@name": "Menú de categorías de dos columnas", "number-pad/@description": "Muestra el teclado numérico virtual de Scratch cuando se editan entradas de números en bloques en todos los dispositivos y no solo en dispositivos táctiles.", "number-pad/@info-explanation": "Se mostrará un teclado numérico cuando se editen entradas para bloques como \"mover () pasos\".", "number-pad/@name": "Siempre mostrar teclado numérico", "editor-number-arrow-keys/@description": "Use las teclas de arriba y abajo para incrementar o decrementar valores numéricos en campos númericos, como los que tienen los bloques, o los del area de propiedades del objeto.", "editor-number-arrow-keys/@name": "Incremento con teclas de flecha en editor", "editor-number-arrow-keys/@settings-name-alt": "Cambio en Alt+Tecla", "editor-number-arrow-keys/@settings-name-altCustom": "Cambio en Alt+Tecla", "editor-number-arrow-keys/@settings-name-regular": "Cambio en pulsación de tecla normal", "editor-number-arrow-keys/@settings-name-regularCustom": "Cambio en pulsación de tecla normal", "editor-number-arrow-keys/@settings-name-shift": "Cambio en Shift+Tecla", "editor-number-arrow-keys/@settings-name-shiftCustom": "Cambio en Shift+Tecla", "editor-number-arrow-keys/@settings-name-useCustom": "Usar valores personalizados", "editor-number-arrow-keys/@settings-select-alt-none": "<PERSON><PERSON><PERSON>", "editor-number-arrow-keys/@settings-select-regular-none": "<PERSON><PERSON><PERSON>", "editor-number-arrow-keys/@settings-select-shift-none": "<PERSON><PERSON><PERSON>", "expanded-backpack/@description": "Ordena los contenidos de la mochila en una cuadrícula de dos direcciones expandible, similar a la librería de disfraces y aumenta el tamaño de ciertas miniaturas.", "expanded-backpack/@name": "<PERSON><PERSON><PERSON><PERSON> filas en la mochila", "expanded-backpack/@settings-name-rows": "Número de filas mostradas", "expanded-backpack/@settings-name-upscale": "Miniaturas de objetos y scripts más grandes", "script-snap/@description": "Arrastre un script para automáticamente ajustar su posición a los puntos del área de código.", "script-snap/@name": "Ajustar scripts a cuadrícula", "script-snap/@preset-name-default": "Predeterminado", "script-snap/@preset-name-half-block": "Medio bloque", "script-snap/@preset-name-whole-block": "Bloque entero", "script-snap/@settings-name-grid": "<PERSON><PERSON><PERSON> de la grilla (px)", "fullscreen/@description": "Arregla algunos efectos no deseados del modo de pantalla completa del reproductor de proyectos, abre el reproductor en modo de pantalla completa de su navegador y esconde la barra de herramientas de bandera verde.", "fullscreen/@name": "Pantalla completa mejorada", "fullscreen/@settings-name-browserFullscreen": "<PERSON><PERSON>r reproductor de proyectos en modo pantalla completa del navegador", "hide-stage/@description": "Agrega un botón al lado de los botones de \"escenario pequeño\" y \"escenario grande\" que esconde el escenario y el panel de objetos, haciendo mucho más grande el área de código.", "hide-stage/@name": "Esconder escenario y panel de objetos", "editor-stepping/@description": "Agrega un borde colorido a los bloques que están ejecutándose actualmente en un proyecto.", "editor-stepping/@name": "Borde de bloque en ejecución", "editor-stepping/@settings-name-highlight-color": "Color de destacado"}