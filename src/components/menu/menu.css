@import "../../css/colors.css";

.menu {
    position: absolute;
    border: 1px solid $ui-black-transparent;
    border-radius: 0 0 8px 8px;
    background-color: $menu-bar-background;
    padding: 0;
    margin: 0;
    min-width: 186px;
    overflow: visible;
    box-shadow: 0 8px 8px 0 $shadow;
}

.menu.left {
    right: 0;
}

.menu.right {
    left: 0;
}

.menu-item {
    display: block;
    line-height: 34px;
    white-space: nowrap;
    padding: 0 10px;
    font-size: .75rem;
    margin: 0;
    font-weight: bold;
    position: relative;
}

.menu-item.active,
.menu-item:hover,
.menu-item.expanded {
    background-color: $ui-black-transparent;
}

.menu-item.hoverable {
    cursor: pointer;
}

.menu-section {
    border-top: 1px solid $ui-black-transparent;
}

.submenu {
    display: none;
    position: absolute;
    top: -1px;
    min-width: 186px;
    height: max-content;
    overflow: hidden;
    border-radius: 8px;
}

.submenu.right {
    left: 100%;
}

.submenu.left {
    right: 100%;
}

.submenu > .menu {
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: $menu-bar-foreground $menu-bar-background;
    border-radius: 8px;
}

.menu-item.expanded > .submenu {
    display: block;
}

:not(:has(> .menu-item.expanded)) > .menu-item:hover > .submenu {
    display: block;
}

/* Fallback styling for webkit browsers that don't support scrollbar-color */

.submenu > .menu::-webkit-scrollbar {
    width: 12px;
}

.submenu > .menu::-webkit-scrollbar-track {
    background: $menu-bar-background;
}

.submenu > .menu::-webkit-scrollbar-thumb {
    background-color: $menu-bar-foreground;
    border-radius: 12px;
    border: 3px solid $menu-bar-background;
}
